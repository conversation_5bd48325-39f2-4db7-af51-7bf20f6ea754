// Direct hamburger menu implementation for French version
document.addEventListener('DOMContentLoaded', function() {
    // Get the navigation elements
    const navToggle = document.querySelector('.mobile-nav-toggle');
    const primaryNav = document.getElementById('primary-navigation');
    
    // Check if elements exist
    if (navToggle && primaryNav) {
        // Add click event to toggle button
        navToggle.addEventListener('click', function() {
            // Get current state
            const isVisible = primaryNav.getAttribute('data-visible') === 'true';
            
            // Toggle visibility
            primaryNav.setAttribute('data-visible', !isVisible);
            navToggle.setAttribute('aria-expanded', !isVisible);
        });
        
        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (primaryNav.getAttribute('data-visible') === 'true' &&
                !primaryNav.contains(e.target) &&
                e.target !== navToggle &&
                !navToggle.contains(e.target)) {
                primaryNav.setAttribute('data-visible', 'false');
                navToggle.setAttribute('aria-expanded', 'false');
            }
        });
        
        // Close menu when clicking on links
        const navLinks = primaryNav.querySelectorAll('a');
        navLinks.forEach(function(link) {
            link.addEventListener('click', function() {
                primaryNav.setAttribute('data-visible', 'false');
                navToggle.setAttribute('aria-expanded', 'false');
            });
        });
    }
});