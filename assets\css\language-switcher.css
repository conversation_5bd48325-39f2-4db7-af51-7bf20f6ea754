/* Language Switcher Styles */
.language-switcher {
    position: relative;
    margin-left: 1rem;
    display: inline-block;
    z-index: 1001; /* Ensure it's above other elements */
}

.language-dropdown {
    position: relative;
}

.language-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: #8B4513;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
    color: white;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.language-btn:hover {
    background: #7a3d11;
    border-color: rgba(255, 255, 255, 0.5);
}

.language-text {
    font-size: 0.875rem;
    font-weight: 600;
}

.dropdown-arrow {
    transition: transform 0.3s ease;
}

/* Hide menu by default */
.language-menu {
    position: absolute;
    top: calc(100% + 0.5rem);
    right: 0;
    background: white;
    border: 1px solid #e5e0da;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    min-width: 160px;
    display: none;
    z-index: 1002;
}

/* Show menu only when .show class is added */
.language-menu.show {
    display: block;
}

.language-option {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    width: 100%;
    padding: 0.75rem 1rem;
    background: transparent;
    border: none;
    color: #2a2a2a;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
    text-align: left;
    text-decoration: none;
}

.language-option:first-child {
    border-radius: 8px 8px 0 0;
}

.language-option:last-child {
    border-radius: 0 0 8px 8px;
}

.language-option:hover {
    background: #f7f5f2;
}

.language-option.active {
    background: #8B4513;
    color: white;
}

.language-option.active:hover {
    background: #7a3d11;
}

/* Flag Icons - Fixed alignment */
.flag-icon {
    width: 24px;
    margin-left: 5px;
    height: 18px;
    border-radius: 3px;
    background-size: cover;
    background-position: center;
    flex-shrink: 0;
    display: inline-block;
    vertical-align: middle;
}

.flag-en {
    background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA2MCAzMCI+PHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjMwIiBmaWxsPSIjMDAyNDdkIi8+PHBhdGggZD0iTTAsMCBMNjAsMzAgTTYwLDAgTDAsMzAiIHN0cm9rZT0iI2ZmZiIgc3Ryb2tlLXdpZHRoPSI0Ii8+PHBhdGggZD0iTTMwLDAgTDMwLDMwIE0wLDE1IEw2MCwxNSIgc3Ryb2tlPSIjZmZmIiBzdHJva2Utd2lkdGg9IjYiLz48cGF0aCBkPSJNMzAsMCBMMzAsMzAgTTAsMTUgTDYwLDE1IiBzdHJva2U9IiNjZjE0MmIiIHN0cm9rZS13aWR0aD0iNCIvPjxwYXRoIGQ9Ik0wLDAgTDYwLDMwIE02MCwwIEwwLDMwIiBzdHJva2U9IiNjZjE0MmIiIHN0cm9rZS13aWR0aD0iMiIvPjwvc3ZnPg==');
}

.flag-fr {
    background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA5MCA2MCI+PHJlY3Qgd2lkdGg9IjMwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjMDAyMzk1Ii8+PHJlY3QgeD0iMzAiIHdpZHRoPSIzMCIgaGVpZ2h0PSI2MCIgZmlsbD0iI2ZmZiIvPjxyZWN0IHg9IjYwIiB3aWR0aD0iMzAiIGhlaWdodD0iNjAiIGZpbGw9IiNlZDI5MzkiLz48L3N2Zz4=');
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .language-switcher {
        margin-left: 0;
        margin-top: 115rem;
        width: 100%; /* Full width on mobile */
        position: static; /* Static position on mobile */
    }
    
    .language-btn {
        width: 100%; /* Full width button on mobile */
        justify-content: center; /* Center content */
    }
    
    .language-menu {
        width: 100%; /* Full width menu on mobile */
        position: relative; /* Relative position to prevent cutoff */
        top: 0.5rem; /* Less distance from button */
        left: 0;
        right: auto;
    }
    
    /* Ensure the menu is visible when shown on mobile */
    .language-menu.show {
        display: block;
        position: relative;
        margin-top: 0.5rem;
    }
}

/* Header specific adjustments */
header .nav-links .language-switcher {
    display: flex;
    align-items: center;
}

@media (max-width: 768px) {
    header .nav-links[data-visible="true"] {
        padding-bottom: 80px; /* Add extra padding at the bottom to ensure visibility */
        max-height: calc(100vh - 0px); /* Limit height and enable scrolling */
        overflow-y: auto;
    }
    
    header .nav-links[data-visible="true"] .language-switcher {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid rgba(139, 69, 19, 0.2);
        margin-bottom: 1rem; /* Add space at the bottom */
        position: relative;
        bottom: auto;
    }
}