<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-translate="meta.title">Galerie | Hôtel Kasbah Atfel, Vallée d'Ourika</title>
    <meta name="description" data-translate="meta.description"
        content="Explorez la beauté époustouflante de la Kasbah Atfel et de la vallée d'Ourika environnante à travers notre galerie de photos.">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="hamburger-menu-fix.css">
    <link rel="stylesheet" href="assets/css/language-switcher.css">

    <!-- Canonical URL -->
    <link rel="canonical" href="https://www.atfelkasbah.com/fr/gallery">
    <link rel="alternate" hreflang="en" href="https://www.atfelkasbah.com/gallery">
    <link rel="alternate" hreflang="fr" href="https://www.atfelkasbah.com/fr/gallery">

    <!-- Fallback for when JavaScript is disabled -->
    <noscript>
        <style>
            .mobile-nav-toggle {
                display: none;
            }

            .nav-links {
                display: block !important;
                position: static !important;
                transform: none !important;
                background: transparent !important;
                width: 100% !important;
                pointer-events: auto !important;
            }
        </style>
    </noscript>
    <style>
        :root {
            --primary-color: #8B4513;
            /* SaddleBrown */
            --secondary-color: #f7f5f2;
            /* A warmer, softer white */
            --text-dark: #2a2a2a;
            --text-light: #6b6b6b;
            --white: #ffffff;
            --border-color: #e5e0da;
        }

        /* --- Gallery Page Specific Styles --- */
        .gallery-hero {
            padding-top: 120px;
            padding-bottom: 60px;
        }

        .gallery-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .gallery-header h1 {
            font-size: 3rem;
            font-weight: 700;
            color: var(--text-dark);
        }

        /* Filter Buttons */
        .gallery-filters {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 40px;
        }

        .filter-btn {
            background-color: transparent;
            border: 1px solid var(--border-color);
            border-radius: 30px;
            padding: 12px 24px;
            font-size: 1rem;
            font-weight: 500;
            color: var(--text-light);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-btn:hover {
            background-color: var(--secondary-color);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .filter-btn.active {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--white);
        }

        /* Image Grid */
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .grid-item {
            border-radius: 12px;
            overflow: hidden;
            cursor: pointer;
            position: relative;
            aspect-ratio: 1 / 1;
            /* Makes images square */
        }

        .grid-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.4s ease;
        }

        .grid-item:hover img {
            transform: scale(1.05);
        }

        /* Styles for hidden items for filtering */
        .grid-item.hide {
            display: none;
        }

        /* --- Lightbox Modal --- */
        .lightbox-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            display: none;
            /* Changed from flex */
            justify-content: center;
            align-items: center;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .lightbox-modal.visible {
            display: flex;
            /* Show the modal */
            opacity: 1;
        }

        .lightbox-content {
            position: relative;
            width: 90%;
            height: 90%;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .lightbox-content img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        /* Lightbox Navigation */
        .lightbox-close {
            position: absolute;
            top: 15px;
            right: 25px;
            font-size: 2.5rem;
            color: #fff;
            cursor: pointer;
            line-height: 1;
            z-index: 1002;
        }

        .lightbox-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 44px;
            height: 44px;
            background-color: rgba(0, 0, 0, 0.4);
            color: #fff;
            border: none;
            border-radius: 50%;
            font-size: 1.5rem;
            font-weight: bold;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: background-color 0.3s ease;
            z-index: 1001;
        }

        .lightbox-nav:hover {
            background-color: rgba(0, 0, 0, 0.7);
        }

        .lightbox-prev {
            left: 15px;
        }

        .lightbox-next {
            right: 15px;
        }
    </style>
</head>

<body>

    <header id="header">
        <!-- Re-using the same header for consistency -->
        <nav class="container">
            <a href="index-fr.html" class="logo">Atfel Kasbah</a>
            <button class="mobile-nav-toggle" aria-controls="primary-navigation" aria-expanded="false">
                <span class="line line-top"></span>
                <span class="line line-middle"></span>
                <span class="line line-bottom"></span>
            </button>
            <ul id="primary-navigation" data-visible="false" class="nav-links">
                <li><a href="index-fr.html#rooms" data-translate="nav.rooms">Chambres</a></li>
                <li><a href="gallery-fr.html" data-translate="nav.gallery">Galerie</a></li>
                <li class="cta-item"><a href="index-fr.html#rooms" class="cta-button" data-translate="nav.book_now">Réserver</a></li>
                <li><a href="aboutus.html" data-translate="nav.about">À Propos</a></li>
                <li><a href="contactus.html" data-translate="nav.contact">Contact</a></li>
                
                <!-- Language Switcher -->
                <li class="language-switcher">
                    <div class="language-dropdown">
                        <button class="language-btn" aria-label="Switch language">
                            <span class="flag-icon flag-fr"></span>
                            <span class="language-text">FR</span>
                        </button>
                        <div class="language-menu">
                            <a href="gallery.html" class="language-option" data-lang="en">
                                <span class="flag-icon flag-en"></span>
                                <span>English</span>
                            </a>
                            <a href="gallery-fr.html" class="language-option active" data-lang="fr">
                                <span class="flag-icon flag-fr"></span>
                                <span>Français</span>
                            </a>
                        </div>
                    </div>
                </li>
            </ul>
        </nav>
    </header>

    <main>
        <section class="gallery-hero">
            <div class="container">
                <div class="gallery-header">
                    <h1 data-translate="gallery.title">Notre Galerie</h1>
                </div>

                <!-- Filter Buttons -->
                <div class="gallery-filters">
                    <button class="filter-btn active" data-filter="all" data-translate="gallery.filters.all">Tout</button>
                    <button class="filter-btn" data-filter="kasbah" data-translate="gallery.filters.kasbah">Kasbah Atfel</button>
                    <button class="filter-btn" data-filter="spa" data-translate="gallery.filters.spa">Spa</button>
                    <button class="filter-btn" data-filter="ourika" data-translate="gallery.filters.ourika">Ourika</button>
                </div>

                <!-- Image Grid -->
                <div class="image-grid">
                    <!-- Add your images here. Use the 'data-category' attribute to assign them a filter. -->
                    <div class="grid-item" data-category="kasbah"><img src="assets/image copy.png"
                            alt="Vue de l'extérieur de la Kasbah"></div>
                    <div class="grid-item" data-category="kasbah"><img
                                src="Gallery-Hotel_Kasbah_Atfel_Ourika\affordable-hotel-Kasbah-atfel-ourika-in-morocco23.jpg"
                                alt="Paysage de la vallée d'Ourika"></div>    
                    <div class="grid-item" data-category="kasbah"><img src="Gallery-Hotel_Kasbah_Atfel_Ourika\affordable-hotel-Kasbah-atfel-ourika-in-morocco27.jpg"
                                    alt="Vue de l'extérieur de la Kasbah"></div>
                    <div class="grid-item" data-category="kasbah"><img
                                        src="Gallery-Hotel_Kasbah_Atfel_Ourika\affordable-hotel-Kasbah-atfel-ourika-in-morocco26.jpg"
                                        alt="Paysage de la vallée d'Ourika"></div>
                    <div class="grid-item" data-category="kasbah"><img
                                            src="Gallery-Hotel_Kasbah_Atfel_Ourika\affordable-hotel-Kasbah-atfel-ourika-in-morocco28.jpg"
                                            alt="Paysage de la vallée d'Ourika"></div>                   
                    <div class="grid-item" data-category="kasbah"><img src="assets/image copy 3.png"
                            alt="Intérieur d'une suite à la Kasbah"></div>
                    <div class="grid-item" data-category="kasbah"><img
                            src="Gallery-Hotel_Kasbah_Atfel_Ourika\affordable-hotel-Kasbah-atfel-ourika-in-morocco.jpg"
                            alt="Vue de l'extérieur de la Kasbah"></div>
                    <div class="grid-item" data-category="kasbah"><img
                            src="Gallery-Hotel_Kasbah_Atfel_Ourika\affordable-hotel-Kasbah-atfel-ourika-in-morocco1.jpg"
                            alt="Paysage de la vallée d'Ourika"></div>
                    <div class="grid-item" data-category="kasbah"><img
                            src="Gallery-Hotel_Kasbah_Atfel_Ourika\affordable-hotel-Kasbah-atfel-ourika-in-morocco2.jpg"
                            alt="Paysage de la vallée d'Ourika"></div>
                    <div class="grid-item" data-category="kasbah"><img
                            src="Gallery-Hotel_Kasbah_Atfel_Ourika\affordable-hotel-Kasbah-atfel-ourika-in-morocco3.jpg"
                            alt="Paysage de la vallée d'Ourika"></div>
                    <div class="grid-item" data-category="kasbah"><img
                            src="Gallery-Hotel_Kasbah_Atfel_Ourika\affordable-hotel-Kasbah-atfel-ourika-in-morocco4.jpg"
                            alt="Paysage de la vallée d'Ourika"></div>
                    <div class="grid-item" data-category="kasbah"><img
                            src="Gallery-Hotel_Kasbah_Atfel_Ourika\affordable-hotel-Kasbah-atfel-ourika-in-morocco5.jpg"
                            alt="Paysage de la vallée d'Ourika"></div>
                    <div class="grid-item" data-category="kasbah"><img
                            src="Gallery-Hotel_Kasbah_Atfel_Ourika\affordable-hotel-Kasbah-atfel-ourika-in-morocco6.jpg"
                            alt="Paysage de la vallée d'Ourika"></div>
                    <div class="grid-item" data-category="kasbah"><img
                            src="Gallery-Hotel_Kasbah_Atfel_Ourika\affordable-hotel-Kasbah-atfel-ourika-in-morocco7.jpg"
                            alt="Paysage de la vallée d'Ourika"></div>
                    <div class="grid-item" data-category="kasbah"><img
                            src="Gallery-Hotel_Kasbah_Atfel_Ourika\affordable-hotel-Kasbah-atfel-ourika-in-morocco8.jpg"
                            alt="Paysage de la vallée d'Ourika"></div>
                    <div class="grid-item" data-category="kasbah"><img
                            src="Gallery-Hotel_Kasbah_Atfel_Ourika\affordable-hotel-Kasbah-atfel-ourika-in-morocco9.jpg"
                            alt="Paysage de la vallée d'Ourika"></div>
                    <div class="grid-item" data-category="kasbah"><img
                            src="Gallery-Hotel_Kasbah_Atfel_Ourika\affordable-hotel-Kasbah-atfel-ourika-in-morocco10.jpg"
                            alt="Paysage de la vallée d'Ourika"></div>
                    <div class="grid-item" data-category="kasbah"><img
                            src="Gallery-Hotel_Kasbah_Atfel_Ourika\affordable-hotel-Kasbah-atfel-ourika-in-morocco11.jpg"
                            alt="Paysage de la vallée d'Ourika"></div>
                    <div class="grid-item" data-category="kasbah"><img
                            src="Gallery-Hotel_Kasbah_Atfel_Ourika\affordable-hotel-Kasbah-atfel-ourika-in-morocco12.jpg"
                            alt="Paysage de la vallée d'Ourika"></div>
                    <div class="grid-item" data-category="kasbah"><img
                            src="Gallery-Hotel_Kasbah_Atfel_Ourika\affordable-hotel-Kasbah-atfel-ourika-in-morocco13.jpg"
                            alt="Paysage de la vallée d'Ourika"></div>
                    <div class="grid-item" data-category="kasbah"><img
                            src="Gallery-Hotel_Kasbah_Atfel_Ourika\affordable-hotel-Kasbah-atfel-ourika-in-morocco14.jpg"
                            alt="Paysage de la vallée d'Ourika"></div>
                    <div class="grid-item" data-category="kasbah"><img
                            src="Gallery-Hotel_Kasbah_Atfel_Ourika\affordable-hotel-Kasbah-atfel-ourika-in-morocco15.jpg"
                            alt="Paysage de la vallée d'Ourika"></div>
                    <div class="grid-item" data-category="kasbah"><img
                            src="Gallery-Hotel_Kasbah_Atfel_Ourika\affordable-hotel-Kasbah-atfel-ourika-in-morocco16.jpg"
                            alt="Paysage de la vallée d'Ourika"></div>
                    <div class="grid-item" data-category="kasbah"><img
                            src="Gallery-Hotel_Kasbah_Atfel_Ourika\affordable-hotel-Kasbah-atfel-ourika-in-morocco17.jpg"
                            alt="Paysage de la vallée d'Ourika"></div>
                    <div class="grid-item" data-category="kasbah"><img
                            src="Gallery-Hotel_Kasbah_Atfel_Ourika\affordable-hotel-Kasbah-atfel-ourika-in-morocco18.jpg"
                            alt="Paysage de la vallée d'Ourika"></div>
                    <div class="grid-item" data-category="kasbah"><img
                            src="Gallery-Hotel_Kasbah_Atfel_Ourika\affordable-hotel-Kasbah-atfel-ourika-in-morocco19.jpg"
                            alt="Paysage de la vallée d'Ourika"></div>
                    <div class="grid-item" data-category="kasbah"><img
                            src="Gallery-Hotel_Kasbah_Atfel_Ourika\affordable-hotel-Kasbah-atfel-ourika-in-morocco20.jpg"
                            alt="Paysage de la vallée d'Ourika"></div>
                    <div class="grid-item" data-category="kasbah"><img
                            src="Gallery-Hotel_Kasbah_Atfel_Ourika\affordable-hotel-Kasbah-atfel-ourika-in-morocco21.jpg"
                            alt="Paysage de la vallée d'Ourika"></div>
                    <div class="grid-item" data-category="kasbah"><img
                            src="Gallery-Hotel_Kasbah_Atfel_Ourika\affordable-hotel-Kasbah-atfel-ourika-in-morocco22.jpg"
                            alt="Paysage de la vallée d'Ourika"></div>
                    <div class="grid-item" data-category="kasbah"><img
                            src="Gallery-Hotel_Kasbah_Atfel_Ourika\affordable-hotel-Kasbah-atfel-ourika-in-morocco24.jpg"
                            alt="Paysage de la vallée d'Ourika"></div>
                    <div class="grid-item" data-category="kasbah"><img
                            src="Gallery-Hotel_Kasbah_Atfel_Ourika\affordable-hotel-Kasbah-atfel-ourika-in-morocco25.jpg"
                            alt="Paysage de la vallée d'Ourika"></div>


                    <div class="grid-item" data-category="spa"><img src="Spa\Hotel-Kasbah-Atfel-Spa-Jacuzzi.jpg" alt="Spa et Jacuzzi"></div>     
                    <div class="grid-item" data-category="spa"><img src="Spa\Hotel-Kasbah-Atfel-Spa-Jacuzzi1.jpg" alt="Spa et Jacuzzi"></div>     
                    <div class="grid-item" data-category="spa"><img src="Spa\Hotel-Kasbah-Atfel-Spa-Jacuzzi2.jpg" alt="Spa et Jacuzzi"></div>  
                    <div class="grid-item" data-category="spa"><img src="Spa\Hotel-Kasbah-Atfel-Spa-Jacuzzi3.jpg" alt="Spa et Jacuzzi"></div>     
                    <div class="grid-item" data-category="spa"><img src="Spa\Hotel-Kasbah-Atfel-Spa-Jacuzzi4.jpg" alt="Spa et Jacuzzi"></div>  
                    <div class="grid-item" data-category="spa"><img src="Spa\Hotel-Kasbah-Atfel-Spa-Jacuzzi5.jpg" alt="Spa et Jacuzzi"></div>     
                    <div class="grid-item" data-category="spa"><img src="Spa\Hotel-Kasbah-Atfel-Spa-Jacuzzi6.jpg" alt="Spa et Jacuzzi"></div>
                    <div class="grid-item" data-category="spa"><img src="Spa\Hotel-Kasbah-Atfel-Spa-Jacuzzi7.jpg" alt="Spa et Jacuzzi"></div>     
                    <div class="grid-item" data-category="spa"><img src="Spa\Hotel-Kasbah-Atfel-Spa-Jacuzzi8.jpg" alt="Spa et Jacuzzi"></div> 
                    <div class="grid-item" data-category="spa"><img src="Spa\Hotel-Kasbah-Atfel-Spa-Jacuzzi9.jpg" alt="Spa et Jacuzzi"></div>      

                </div>
            </div>
        </section>
    </main>

    <!-- Lightbox Modal -->
    <div id="lightbox-modal" class="lightbox-modal">
        <span id="lightbox-close" class="lightbox-close">×</span>
        <button id="lightbox-prev" class="lightbox-nav lightbox-prev">‹</button>
        <div class="lightbox-content">
            <img src="" id="lightbox-image" alt="Image de galerie en plein écran">
        </div>
        <button id="lightbox-next" class="lightbox-nav lightbox-next">›</button>
    </div>

    <!-- =========================================
       START: Custom Testimonial Slider
       ========================================= -->
    <section class="testimonial-slider-section">
        <div class="container">
            <h2 class="section-title" data-translate="testimonials.title">Ce Que Disent Nos Clients</h2>
        </div>

        <div class="testimonial-slider-container" id="testimonial-slider">
            <!-- Testimonial Cards will be dynamically inserted here by JavaScript -->
        </div>

        <div class="slider-nav">
            <button id="prev-btn" class="slider-btn" aria-label="Témoignage précédent">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
                </svg>
            </button>
            <button id="next-btn" class="slider-btn" aria-label="Témoignage suivant">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                </svg>
            </button>
        </div>
    </section>
    <!-- =========================================
       END: Custom Testimonial Slider
       ========================================= -->

    <footer id="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Atfel Kasbah</h3>
                    <p data-translate="footer.description">Découvrez l'hospitalité marocaine authentique au cœur de la vallée d'Ourika.</p>
                </div>
                <div class="footer-section">
                    <h3 data-translate="footer.quick_links">Liens Rapides</h3>
                    <ul>
                        <li><a href="index-fr.html" data-translate="nav.home">Accueil</a></li>
                        <li><a href="index-fr.html#rooms" data-translate="nav.rooms">Chambres & Suites</a></li>
                        <li><a href="gallery-fr.html" data-translate="nav.gallery">Galerie</a></li>
                        <li><a href="aboutus.html" data-translate="nav.about">À Propos</a></li>
                        <li><a href="blog.html">Blog / News</a></li>
                        <li><a href="faq.html">FAQ</a></li>
                        <li><a href="contactus.html" data-translate="nav.contact">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3 data-translate="footer.contact_info">Informations de Contact</h3>
                    <ul>
                        <li>📍 Vallée d'Ourika, Maroc</li>
                        <li>📞 +212 XXX XXXXXX</li>
                        <li>✉️ <EMAIL></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3 data-translate="footer.legal">Légal</h3>
                    <ul>
                        <li><a href="#" data-translate="footer.privacy">Politique de Confidentialité</a></li>
                        <li><a href="#" data-translate="footer.terms">Conditions Générales</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p data-translate="footer.copyright">© 2025 Hôtel Kasbah Atfel. Tous droits réservés.</p>
            </div>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // --- Mobile Navigation ---
            const navToggle = document.querySelector('.mobile-nav-toggle');
            const primaryNav = document.getElementById('primary-navigation');
            if (navToggle) {
                navToggle.addEventListener('click', () => {
                    const isVisible = primaryNav.getAttribute('data-visible') === 'true';
                    primaryNav.setAttribute('data-visible', !isVisible);
                    navToggle.setAttribute('aria-expanded', !isVisible);
                });
            }

            // --- Gallery Logic ---
            const filterContainer = document.querySelector('.gallery-filters');
            const galleryItems = document.querySelectorAll('.grid-item');
            const lightbox = document.getElementById('lightbox-modal');
            const lightboxImg = document.getElementById('lightbox-image');
            const closeBtn = document.getElementById('lightbox-close');
            const prevBtn = document.getElementById('lightbox-prev');
            const nextBtn = document.getElementById('lightbox-next');

            let currentImageIndex;
            let visibleItems = [];

            // Filter functionality
            if (filterContainer) {
                filterContainer.addEventListener('click', (e) => {
                    if (e.target.classList.contains('filter-btn')) {
                        // Update active button
                        filterContainer.querySelector('.active').classList.remove('active');
                        e.target.classList.add('active');

                        const filterValue = e.target.getAttribute('data-filter');

                        galleryItems.forEach(item => {
                            if (filterValue === 'all' || item.getAttribute('data-category') === filterValue) {
                                item.classList.remove('hide');
                            } else {
                                item.classList.add('hide');
                            }
                        });
                    }
                });
            }

            // Lightbox functionality
            galleryItems.forEach(item => {
                item.addEventListener('click', () => {
                    openLightbox(item);
                });
            });

            const openLightbox = (item) => {
                // Get only the currently visible images for swiping
                visibleItems = Array.from(galleryItems).filter(i => !i.classList.contains('hide'));
                const clickedImgSrc = item.querySelector('img').src;
                currentImageIndex = visibleItems.findIndex(visibleItem => visibleItem.querySelector('img').src === clickedImgSrc);

                lightbox.classList.add('visible');
                updateLightboxImage();
            };

            const closeLightbox = () => {
                lightbox.classList.remove('visible');
            };

            const updateLightboxImage = () => {
                const item = visibleItems[currentImageIndex];
                const imgSrc = item.querySelector('img').src;
                lightboxImg.src = imgSrc;
            };

            const showPrevImage = () => {
                currentImageIndex = (currentImageIndex - 1 + visibleItems.length) % visibleItems.length;
                updateLightboxImage();
            };

            const showNextImage = () => {
                currentImageIndex = (currentImageIndex + 1) % visibleItems.length;
                updateLightboxImage();
            };

            // Event listeners
            closeBtn.addEventListener('click', closeLightbox);
            prevBtn.addEventListener('click', showPrevImage);
            nextBtn.addEventListener('click', showNextImage);

            // Close lightbox by clicking on the background
            lightbox.addEventListener('click', (e) => {
                if (e.target === lightbox) {
                    closeLightbox();
                }
            });
        });
    </script>
    <script src="assets/js/main.js" defer></script>
    <script src="hamburger-menu-fix.js" defer></script>
    <script src="assets/js/language-switcher.js" defer></script>
</body>

</html>