
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');
body {
    width: 100%;
    overflow-x: hidden;
}
:root {
    --primary-gold: #C9A96E;
    --primary-dark: #8B7355;
    --secondary-sage: #9CAF88;
    --accent-terracotta: #D2691E;
    --neutral-cream: #F8F6F0;
    --neutral-warm-white: #FEFCF7;
    --text-charcoal: #2C2C2C;
    --text-medium: #4A4A4A;
    --text-light: #757575;
    --luxury-navy: #1E293B;
    --booking-blue: #003580;
    --rating-green: #227950;
    --shadow-soft: rgba(201, 169, 110, 0.1);
    --shadow-medium: rgba(201, 169, 110, 0.2);
    --shadow-strong: rgba(44, 44, 44, 0.15);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-charcoal);
    overflow-x: hidden;
    background: var(--neutral-warm-white);
}

/* Header Styles */
header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: var(--neutral-warm-white);
    backdrop-filter: blur(20px);
    z-index: 1000;
    padding: 1rem 0;
    padding-top: calc(1rem + env(safe-area-inset-top));
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(201, 169, 110, 0.1);
}

header.scrolled {
    background: rgba(254, 252, 247, 0.98);
    box-shadow: 0 8px 32px rgba(201, 169, 110, 0.15);
    left: 0;
    right: 0;
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.logo {
    font-family: 'Playfair Display', serif;
    font-size: 2.2rem;
    font-weight: 600;
    color: var(--primary-gold);
    text-decoration: none;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    z-index: 1001;
    /* Ensure logo is above mobile nav background */
}

.logo:hover {
    color: var(--primary-dark);
    transform: scale(1.02);
}

.nav-links {
    display: flex;
    list-style: none;
    align-items: center;
    justify-content: flex-end;
    flex-grow: 1;
}

.nav-links li {
    transition: margin 0.4s ease;
}

.nav-links li:not(.cta-item) {
    margin: 0 1rem;
}

.nav-links a:not(.cta-button) {
    color: var(--text-charcoal);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 0;
    white-space: nowrap;
}

.nav-links a:not(.cta-button):hover {
    color: var(--primary-gold);
    transform: translateY(-1px);
}

.nav-links a:not(.cta-button)::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-gold), var(--accent-terracotta));
    transition: width 0.3s ease;
}

.nav-links a:not(.cta-button):hover::after {
    width: 100%;
}

/* CTA Button Styling (GLOBAL) */
.cta-button {
    background: var(--accent-terracotta);
    color: var(--neutral-warm-white) !important;
    padding: 12px 30px;
    text-decoration: none;
    border-radius: 35px;
    font-weight: 500;
    font-size: 1.1rem;
    font-family: 'Playfair Display', serif;
    font-style: italic;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    white-space: nowrap;
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(201, 169, 110, 0.4);
    background: #b95710;
    color: white !important;
}

/* Make sure the text-link underline doesn't apply to the button */
.cta-button::after {
    display: none !important;
}

/* Hides the CTA item by default for the desktop scroll animation */
li.cta-item {
    margin: 0;
    max-width: 0;
    opacity: 0;
    overflow: hidden;
    transition: max-width 0.5s ease, opacity 0.3s ease-in-out 0.1s, margin 0.5s ease;
    pointer-events: none;
}

/* Shows the CTA item on desktop when scrolled */
header.header-cta-visible .nav-links li:not(.cta-item) {
    margin: 0 1.5rem;
}

header.header-cta-visible li.cta-item {
    max-width: 300px;
    margin: 0 1rem;
    overflow: visible;
    opacity: 1;
    pointer-events: auto;
}

/* Hides the hero button when the header one is visible */
header.header-cta-visible #hero-cta-button {
    opacity: 0;
    transform: translateY(20px);
    pointer-events: none;
}

.mobile-nav-toggle {
    display: none;
    position: absolute;
    z-index: 1001;
    top: 1.5rem;
    right: 2rem;
    width: 2.5rem;
    height: 2.5rem;
    background: transparent;
    border: 0;
    cursor: pointer;
}

.mobile-nav-toggle .line {
    display: block;
    width: 100%;
    height: 3px;
    background-color: var(--text-charcoal);
    border-radius: 2px;
    transition: transform 0.3s ease-out, opacity 0.2s ease-out;
}

.mobile-nav-toggle .line+.line {
    margin-top: 6px;
}

.mobile-nav-toggle[aria-expanded="true"] .line-top {
    transform: translateY(9px) rotate(45deg);
}

.mobile-nav-toggle[aria-expanded="true"] .line-middle {
    opacity: 0;
}

.mobile-nav-toggle[aria-expanded="true"] .line-bottom {
    transform: translateY(-9px) rotate(-45deg);
}

/* --- Hero and other sections remain the same --- */
.hero {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    position: relative;
    overflow: hidden;
    background-color: #1E293B;
}

#hero-video {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    min-width: 100%;
    min-height: 100%;
    width: auto;
    height: auto;
    z-index: 1;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.6), rgba(201, 169, 110, 0.3));
    z-index: 2;
}

.hero-content {
    max-width: 900px;
    padding: 2rem;
    padding-top: 15vh;
    animation: fadeInUp 1.2s ease-out;
    position: relative;
    z-index: 3;
}

.welcome-line {
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0.5rem;
    width: 80%;
    margin-left: auto;
    margin-right: auto;
}

.welcome-line::before,
.welcome-line::after {
    content: '';
    flex-grow: 1;
    height: 1px;
    background: rgba(255, 255, 255, 0.7);
}

.welcome-line span {
    padding: 0 1rem;
    font-size: 1.2rem;
    font-family: 'Playfair Display', serif;
    white-space: nowrap;
}

.hero h1 {
    font-family: 'Playfair Display', serif;
    font-size: 4.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 2px 4px 8px rgba(0, 0, 0, 0.3);
    background: linear-gradient(45deg, #ffffff, #f8f6f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.typing-subtitle-container {
    font-size: 1.3rem;
    margin-bottom: 1.5rem;
    text-shadow: 1px 2px 4px rgba(0, 0, 0, 0.4);
    font-weight: 400;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.95);
    min-height: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.typing-cursor {
    display: inline-block;
    width: 3px;
    height: 1.3rem;
    background-color: white;
    animation: blink 1s infinite;
    vertical-align: middle;
    margin-left: 8px;
}

@keyframes blink {
    0%,
    100% {
        opacity: 1;
    }
    50% {
        opacity: 0;
    }
}

.hero-buttons {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
}

#hero-cta-button {
    transition: all 0.4s ease;
}

.btn-primary {
    background: var(--accent-terracotta);
    color: var(--neutral-warm-white);
    padding: 16px 34px;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.3rem;
    font-family: 'Playfair Display', serif;
    font-style: italic;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(201, 169, 110, 0.5);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    padding: 18px 36px;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.1rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(255, 255, 255, 0.2);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.featured-on {
    padding: 50px 0;
    background: var(--neutral-warm-white);
    border-top: 1px solid var(--shadow-soft);
    border-bottom: 1px solid var(--shadow-soft);
}

.featured-on .container {
    max-width: 100%;
}

.featured-on-title {
    text-align: center;
    font-size: 1rem;
    font-family: 'Inter', sans-serif;
    color: var(--text-light);
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: 2rem;
    font-weight: 500;
}

.logo-scroller {
    overflow: hidden;
    position: relative;
    -webkit-mask-image: linear-gradient(90deg, transparent, #fff 15%, #fff 85%, transparent);
    mask-image: linear-gradient(90deg, transparent, #fff 15%, #fff 85%, transparent);
}

.logo-scroller-inner {
    display: flex;
    align-items: center;
    gap: 5rem;
    width: fit-content;
    animation: scroll 60s linear infinite;
}

@keyframes scroll {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}

.logo-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    transition: opacity 0.3s ease;
}

.logo-item:hover {
    opacity: 0.8;
}

.logo-item-top {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    height: 30px;
}

.logo-item-top svg,
.logo-item-top img {
    height: 100%;
    width: auto;
    flex-shrink: 0;
}

.partner-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-medium);
    white-space: nowrap;
}

.logo-item-bottom {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.rating-value {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--text-charcoal);
}

.stars {
    display: flex;
    color: #ffb400;
}

.stars svg {
    height: 18px;
    width: 18px;
}

.partner-rating {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.score-box {
    color: white;
    font-weight: bold;
    font-size: 0.9rem;
    padding: 2px 6px;
    border-radius: 4px;
    flex-shrink: 0;
}

.score-box.score-box--blue {
    background-color: var(--booking-blue);
}

.score-box.score-box--green {
    background-color: var(--rating-green);
}

.score-box.score-box--orange {
    background-color: var(--rating-green);
}

.rating-text-block {
    line-height: 1;
}

.rating-label {
    font-weight: 600;
    font-size: 0.9rem;
    color: var(--text-medium);
}

.review-count {
    font-size: 0.9rem;
    color: var(--text-light);
    font-weight: 500;
    white-space: nowrap;
}

.section-title {
    text-align: center;
    font-family: 'Playfair Display', serif;
    font-size: 3rem;
    font-weight: 600;
    color: var(--luxury-navy);
    margin-bottom: 4rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-gold), var(--accent-terracotta));
    border-radius: 2px;
}

.experience-section {
    padding: 100px 0;
    background: var(--neutral-warm-white);
}

.experience-block {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 5rem;
    align-items: center;
    margin-bottom: 8rem;
}

.experience-block:last-child {
    margin-bottom: 0;
}

.experience-image img {
    width: 100%;
    border-radius: 20px;
    box-shadow: 0 20px 40px var(--shadow-medium);
    transition: all 0.4s ease;
}

.experience-image:hover img {
    transform: scale(1.03);
    box-shadow: 0 25px 50px var(--shadow-strong);
}

.experience-text {
    line-height: 1.8;
    color: var(--text-medium);
}

.experience-text h3 {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--luxury-navy);
    margin-bottom: 1.5rem;
}

.experience-text p {
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
}

.image-right .experience-image {
    grid-column: 2 / 3;
    grid-row: 1 / 2;
}

.image-right .experience-text {
    grid-column: 1 / 2;
    grid-row: 1 / 2;
}

.rooms-preview {
    padding: 100px 0;
    background: var(--neutral-cream);
}

.filter-controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
}

.filter-btn {
    background: transparent;
    border: 2px solid var(--primary-dark);
    color: var(--primary-dark);
    padding: 10px 25px;
    border-radius: 30px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn:hover {
    background: var(--primary-dark);
    color: white;
    transform: translateY(-2px);
}

.filter-btn.active {
    background: var(--primary-gold);
    border-color: var(--primary-gold);
    color: white;
    box-shadow: 0 5px 15px rgba(201, 169, 110, 0.4);
}

.rooms-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2.5rem;
    margin-top: 4rem;
}

.room-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 15px 35px var(--shadow-soft);
    transition: transform 0.3s ease, box-shadow 0.3s ease, opacity 0.4s ease;
    border: 1px solid rgba(201, 169, 110, 0.1);
    position: relative;
}

.room-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 50px var(--shadow-medium);
}

.room-image {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.room-card:hover .room-image {
    transform: scale(1.05);
}

.room-info {
    padding: 1.5rem;
}

.room-info h3 {
    font-family: 'Playfair Display', serif;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--luxury-navy);
    margin-bottom: 0.8rem;
}

.room-info p {
    color: var(--text-medium);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.room-action-area {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 2rem;
}

.room-price {
    font-size: 1.6rem;
    font-weight: 700;
    color: var(--primary-gold);
    margin: 0;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    line-height: 1.2;
}

.room-price .price-prefix {
    font-size: 0.9rem;
    font-weight: 400;
    color: var(--text-light);
}

.room-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.8rem;
    margin-bottom: 1rem;
}

.room-feature {
    background: var(--neutral-cream);
    color: var(--text-medium);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 500;
    border: 1px solid rgba(201, 169, 110, 0.2);
}

.room-card .btn-primary {
    width: auto;
    padding: 12px 28px;
    font-size: 1rem;
    border-radius: 12px;
    margin-top: 0;
    font-family: 'Inter', sans-serif;
    font-style: normal;
}

footer {
    background: #2C1810;
    color: #FFF;
    padding: 60px 0 30px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    color: var(--accent-terracotta);
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #FFF;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: var(--accent-terracotta);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #444;
    color: #AAA;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* --- START: RESPONSIVE DESIGN --- */
@media (max-width: 920px) {
    .mobile-nav-toggle {
        display: block;
    }
    .nav-links {
        position: fixed;
        top: 0;
        right: 0;
        width: 70vw;
        height: 100vh;
        z-index: 1000;
        flex-direction: column;
        padding: min(20vh, 10rem) 2em;
        gap: 2em;
        justify-content: flex-start;
        align-items: center;
        background: hsla(38, 33%, 97%, 0.95);
        backdrop-filter: blur(1rem);
        transform: translateX(100%);
        transition: transform 350ms ease-out;
        pointer-events: none;
    }
    .nav-links[data-visible="true"] {
        transform: translateX(0%);
        pointer-events: auto;
        /* FIX: Prevents scroll chaining and overscroll bounce */
        overscroll-behavior: contain;
    }
    /* Ensure all navigation links are clickable */
    .nav-links li {
        pointer-events: auto;
        width: 100%;
        text-align: center;
    }
    .nav-links a {
        pointer-events: auto;
        display: block;
        width: 100%;
        padding: 1rem;
        touch-action: manipulation;
    }
    /* Change mobile text links to be larger */
    .nav-links a:not(.cta-button) {
        font-size: 1.5rem;
    }
    /* --- FINAL FIX FOR MOBILE CTA BUTTON --- */
    /* 1. By default on mobile, HIDE the CTA list item completely to remove the gap */
    li.cta-item {
        display: none;
        margin: 0;
        /* Also reset margin here */
    }
    /* 2. WHEN the header is scrolled, SHOW the CTA list item again */
    header.header-cta-visible .nav-links li.cta-item {
        display: list-item;
        /* This brings it back into the layout */
        max-width: initial;
        /* Reset desktop animation styles */
        opacity: 1;
        pointer-events: auto;
    }
    /* --- END OF FIX --- */
    .hero h1 {
        font-size: 3.5rem;
    }
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    .section-title {
        font-size: 2.5rem;
    }
    .experience-block,
    .image-right {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    .image-right .experience-image,
    .image-right .experience-text {
        grid-column: 1 / 2;
        grid-row: auto;
    }
    .image-right .experience-image {
        order: -1;
    }
    .experience-text h3 {
        font-size: 2rem;
    }
    .rooms-grid {
        grid-template-columns: 1fr;
    }
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }
    .hero h1 {
        font-size: 2.8rem;
    }
    .typing-subtitle-container {
        font-size: 1.1rem;
        min-height: 80px;
    }
    .section-title {
        font-size: 2rem;
    }
    .rooms-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
}
/* =========================================
   Custom Testimonial Slider Styles
   ========================================= */

.testimonial-slider-section {
    background-color: #f8f6f4; /* A light background to match the site */
    padding: 80px 0;
    position: relative;
    overflow: hidden;
    user-select: none; /* Prevents text selection during swipe */
}

.testimonial-slider-container {
    position: relative;
    width: 100%;
    height: 400px; /* Adjust height as needed */
    perspective: 1000px; /* Creates a 3D space for the cards */
    display: flex;
    justify-content: center;
    align-items: center;
}

.testimonial-card {
    position: absolute;
    width: 80%;
    max-width: 550px; /* Max width for the cards */
    padding: 30px;
    background-color: #ffffff;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    transition: transform 0.5s ease, opacity 0.5s ease, filter 0.5s ease;
    cursor: grab;
    transform-style: preserve-3d;
    backface-visibility: hidden; /* Improves rendering performance */
}

.testimonial-card:active {
    cursor: grabbing;
}


/* State for the main, active card */
.testimonial-card.active {
    transform: translateX(0) scale(1);
    opacity: 1;
    filter: blur(0px);
    z-index: 10;
    

}

/* State for the card to the left */
.testimonial-card.prev {
    transform: translateX(-50%) scale(0.8);
    opacity: 0.6;
    filter: blur(3px);
    z-index: 5;
}

/* State for the card to the right */
.testimonial-card.next {
    transform: translateX(50%) scale(0.8);
    opacity: 0.6;
    filter: blur(3px);
    z-index: 5;
}

/* Hidden states for cards out of view */
.testimonial-card.hidden-left {
    transform: translateX(-100%) scale(0.6);
    opacity: 0;
    filter: blur(5px);
    z-index: 1;
}

.testimonial-card.hidden-right {
    transform: translateX(100%) scale(0.6);
    opacity: 0;
    filter: blur(5px);
    z-index: 1;
}


.testimonial-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.booking-logo {
    width: 100px;
    margin-right: 15px;
}

.testimonial-author {
    font-size: 1.2rem;
    font-weight: bold;
    color: #333;
}

.testimonial-body {
    font-size: 1rem;
    line-height: 1.6;
    color: #555;
    font-style: italic;
}

.testimonial-body::before {
    content: '“';
    font-size: 3rem;
    color: #8B4513; /* Kasbah brown color */
    font-weight: bold;
    line-height: 0;
    position: relative;
    top: 10px;
    margin-right: 5px;
}

/* Navigation Buttons */
.slider-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    max-width: 700px; /* Keeps buttons within a reasonable area */
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    justify-content: space-between;
    z-index: 20;
    pointer-events: none; /* Allows clicks to go "through" to the cards */
}

.slider-btn {
    pointer-events: all; /* Make buttons clickable */
    background: #fff;
    border: none;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.slider-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.slider-btn svg {
    width: 24px;
    height: 24px;
    color: #8B4513;
}

#prev-btn {
    margin-left: -20px;
}

#next-btn {
    margin-right: -20px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .testimonial-card.prev {
        transform: translateX(-40%) scale(0.7);
    }
    .testimonial-card.next {
        transform: translateX(40%) scale(0.7);
    }
    .slider-nav {
        max-width: 100%;
        padding: 0 10px;
    }
    #prev-btn, #next-btn {
        margin: 0;
    }
}

/* =========================================
   Luxury Awards Section Styles
   ========================================= */

.luxury-awards-section {
    padding: 100px 0;
    background-color: #f9f9f9; /* A very light, soft grey background */
    overflow: hidden; /* Ensures animations don't cause scrollbars */
}

.section-header-centered {
    text-align: center;
    margin-bottom: 80px;
}

.section-header-centered .section-subtitle {
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.awards-container {
    display: flex;
    flex-direction: column;
    gap: 80px; /* Provides ample space between award items */
}

.luxury-award-item {
    display: flex;
    align-items: center;
    gap: 60px;
    /* For the fade-in animation */
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

/* This class will be added by JavaScript when the element is in view */
.luxury-award-item.is-visible {
    opacity: 1;
    transform: translateY(0);
}

.luxury-award-item.reverse-layout {
    flex-direction: row-reverse;
}

.award-image-wrapper {
    flex: 0.7; /* Gives the image a bit more space */
    position: relative;
}

.award-image-wrapper img {
    width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
    display: block;
}

.award-details {
    flex: 1;
    text-align: left;
}

.award-icon {
    margin-bottom: 15px;
    color: #c9ab81; /* A sophisticated, muted gold */
}

.award-icon svg {
    width: 32px;
    height: 32px;
}

.award-title {
    font-size: 2rem; /* Larger, more impactful title */
    font-family: 'Playfair Display', serif; /* Assumes a luxury serif font, adjust if needed */
    margin-bottom: 10px;
    color: #222;
}

.award-partner {
    font-size: 1rem;
    color: #8B4513; /* Your brand brown */
    font-weight: 600;
    margin-bottom: 20px;
    letter-spacing: 0.5px;
}

.award-description {
    font-size: 1rem;
    color: #555;
    line-height: 1.7;
}

/* Responsive styles for tablets and mobile */
@media (max-width: 992px) {
    .luxury-award-item,
    .luxury-award-item.reverse-layout {
        gap: 40px;
    }
    .award-title {
        font-size: 1.75rem;
    }
}

@media (max-width: 768px) {
    .luxury-awards-section {
        padding: 80px 0;
    }
    .luxury-award-item,
    .luxury-award-item.reverse-layout {
        flex-direction: column; /* Stack image and text vertically */
        text-align: center;
    }
    .award-details {
        text-align: center;
    }
}

body.mobile-menu-open {
    overflow: hidden;
}