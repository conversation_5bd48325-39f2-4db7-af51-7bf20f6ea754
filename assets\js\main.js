document.addEventListener('DOMContentLoaded', () => {

    // --- Mobile Navigation --- //
    const nav = document.querySelector('.nav-links');
    const navToggle = document.querySelector('.mobile-nav-toggle');

    navToggle.addEventListener('click', () => {
        const isVisible = nav.getAttribute('data-visible');
        if (isVisible === 'false') {
            nav.setAttribute('data-visible', true);
            navToggle.setAttribute('aria-expanded', true);
        } else {
            nav.setAttribute('data-visible', false);
            navToggle.setAttribute('aria-expanded', false);
        }
    });

    // --- Header Scroll Effects --- //
    const header = document.querySelector('header');
    const heroSection = document.querySelector('.hero');

    const heroObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (!entry.isIntersecting) {
                header.classList.add('header-cta-visible');
            } else {
                header.classList.remove('header-cta-visible');
            }
        });
    }, { threshold: 0.1 });

    if (heroSection) {
        heroObserver.observe(heroSection);
    }

    window.addEventListener('scroll', () => {
        if (window.scrollY > 50) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    });

    // --- Typing Effect --- //
    const typingElement = document.getElementById('typing-subtitle');
    if (typingElement) {
        // This checks for an inline script setup which you might not be using, but it's safe to keep.
        const phrasesAttr = typingElement.getAttribute('data-phrases');
        const phrases = phrasesAttr ? JSON.parse(phrasesAttr) : [
            "Your home away from home.",
            "One of the best views in Morocco.",
            "Experience authentic charm in Ourika Valley."
        ];
        
        let phraseIndex = 0;
        let charIndex = 0;
        let isDeleting = false;

        function type() {
            const currentPhrase = phrases[phraseIndex];
            if (isDeleting) {
                charIndex--;
            } else {
                charIndex++;
            }

            typingElement.textContent = currentPhrase.substring(0, charIndex);

            let typeSpeed = isDeleting ? 75 : 150;

            if (!isDeleting && charIndex === currentPhrase.length) {
                typeSpeed = 2000; // Pause at end
                isDeleting = true;
            } else if (isDeleting && charIndex === 0) {
                isDeleting = false;
                phraseIndex = (phraseIndex + 1) % phrases.length;
                typeSpeed = 500; // Pause before new phrase
            }

            setTimeout(type, typeSpeed);
        }
        if (phrases.length > 0) type();
    }

    // --- Logo Scroller --- //
    const scroller = document.querySelector('.logo-scroller-inner');
    if (scroller) {
        const scrollerContent = Array.from(scroller.children);
        scrollerContent.forEach(item => {
            const duplicatedItem = item.cloneNode(true);
            duplicatedItem.setAttribute('aria-hidden', true);
            scroller.appendChild(duplicatedItem);
        });
    }

    // --- Room Filtering and 'View More' --- //
    const filterContainer = document.querySelector('.filter-controls');
    if (filterContainer) {
        const allRoomCards = Array.from(document.querySelectorAll('.room-card'));
        const viewMoreBtn = document.getElementById('view-more-btn');
        const roomsToShowInitially = 4;

        // This filters based on buttons and shows all that match.
        const filterRooms = (filter) => {
            let visibleCards = [];
            allRoomCards.forEach(card => {
                if (filter === 'all' || card.dataset.category === filter) {
                    card.style.display = 'block';
                    visibleCards.push(card);
                } else {
                    card.style.display = 'none';
                }
            });
             // Hide 'view more' during filtering. Re-evaluate if needed.
            if(viewMoreBtn) viewMoreBtn.style.display = 'none';
        }
        
        // This function is for the initial load of the 'all' tab
        const setupInitialRoomView = () => {
            if (!viewMoreBtn) return; // Only run if view more button exists

            allRoomCards.forEach((card, index) => {
                if(index < roomsToShowInitially) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });

            if (allRoomCards.length > roomsToShowInitially) {
                viewMoreBtn.style.display = 'inline-block';
            } else {
                viewMoreBtn.style.display = 'none';
            }
        };

        filterContainer.addEventListener('click', (e) => {
            if(e.target.matches('.filter-btn')) {
                filterContainer.querySelector('.active')?.classList.remove('active');
                e.target.classList.add('active');
                const filterValue = e.target.getAttribute('data-filter');
                filterRooms(filterValue);
            }
        });

        if (viewMoreBtn) {
            viewMoreBtn.addEventListener('click', () => {
                allRoomCards.forEach(card => {
                    card.style.display = 'block';
                });
                viewMoreBtn.style.display = 'none';
            });
        }
        
        // Setup initial view
        // We assume the 'All' button is active by default.
        setupInitialRoomView();
    }


    // --- Custom Testimonial Slider --- //
    const reviewsData = [
        {
            author: "Eline, Belgium",
            review: "We were very satisfied with our stay. The kasbah offers spacious, tastefully furnished rooms and a serene outdoor area with a well-maintained pool. The atmosphere is warm and welcoming, with a sense of calm throughout. The hostess received us with great kindness, and on arrival, we were met by the subtle fragrance of blossoms — a quiet, memorable welcome."
        },
        {
            author: "Liana, UK",
            review: "Everything at this Kasbah is perfection. Room was outstanding, Aziz & Lessen were very helpful and always on hand to help. Pool, beautiful and refreshing. Whole property was an absolute treat to stay in. If you are looking for somewhere relaxing to stay don’t hesitate to book Kasbah Atfel🥰"
        },
        {
            author: "Sinead, Ireland",
            review: "We loved everything about this property. It’s a beautiful building with a gorgeous swimming pool. The rooms are so comfortable and clean. While the food was delicious, the best thing about our stay was the staff. They were exceptional."
        },
        {
            author: "Ahmed, Netherlands",
            review: "We had a great stay in this hotel/riyadh. The rooms were very spacious and very nicely decorated. The pool is also very pleasant. You constantly have the 1001 night feeling during your stay. Aziz is a very sweet man and a top host and has provided us with all the comforts. This is absolutely recommended and we will definitely come back."
        },
        {
            author: "Hils, Australia",
            review: "So relaxing after hustle and bustle of Marrakech. Met some lovely people here though it was fairly quiet . Felt a bit like a film star with all the attention and the quality of food (much of it grown on site) was fantastic. Lovely gardens."
        },
        
        {
           author:"Adil, Morocco",
           review: "excellent location, in the middle of nature with a healthy atmosphere. the hosts are very welcoming. the food is deliciously home made and served with care and finesse. this is the spot to regenerate one’s mind."

        },

        {
            author: "Caroline, Switzerland",
            review: "The guest house is beautiful in a peaceful place. The room was huge with a modern Moroccan decoration. There is a nice garden with a big swimming pool. Aziz and his wife are lovely people. They were always looking for us to be comfortable. The food is delicious and is a good price. Thank you for their welcoming, we really enjoyed our stay, it was really relaxing. I recommend at 100%"
        },
        {
            author: "Cecile, South Africa",
            review: "the kasbah is beautifully designed and very peaceful, with a view on all sides. The staff were super helpful and friendly, the food tasty and the beds comfortable. Good value for money!"
        },
        {
            author: "Tom, UK",
            review: "A lovely spot from which to go and experience the Atlas Mountains. The gardens and pool area are lovely and peaceful. The staff were helpful - in particular, Abdo took us on a fantastic day out to the Atlas Mountains where we went hiking, had lunch and saw a variety of sights. We were most grateful for his hard work. The property itself was nice if looking for a simple, pleasant stay. The pool was very large (though unheated) and we had very relaxing evenings in the tranquillity of the Kasbah."
        },
        {
            author: "Claudio, Italy",
            review: "We liked, literally, everything! The structure is beautiful and beautifully located, the room we had is clean, spacious, elegant. It's amazing to arrive there and to see the Kasbah from the street for the first time! After a day around, we also had there a delicious dinner. The beds are extremely comfortable while the room is extremely silent. Before leaving we also spent a great time in the wonderful garden with a beautiful swimming pool. The staff is extremely friendly and helpful and take care of everything! We extremely recommend to visit them!"
        },

        
    ];

    const sliderContainer = document.getElementById('testimonial-slider');

    if (sliderContainer) {
        let currentIndex = 0;
        let isDragging = false;
        let startPos = 0;
        let currentTranslate = 0;
        
        // Create and append review cards
        reviewsData.forEach((review, index) => {
            const card = document.createElement('div');
            card.classList.add('testimonial-card');
            card.innerHTML = `
                <div class="testimonial-header">
                    <img src="assets/booking-com-seeklogo.png" alt="Booking.com Logo" class="booking-logo">
                    <span class="testimonial-author">${review.author}</span>
                </div>
                <p class="testimonial-body">${review.review}</p>
            `;
            card.dataset.index = index;
            sliderContainer.appendChild(card);
        });

        const cards = document.querySelectorAll('.testimonial-card');

        function setCardPositions() {
            cards.forEach((card, index) => {
                card.classList.remove('active', 'prev', 'next', 'hidden-left', 'hidden-right');

                if (index === currentIndex) {
                    card.classList.add('active');
                } else if (index === (currentIndex - 1 + cards.length) % cards.length) {
                    card.classList.add('prev');
                } else if (index === (currentIndex + 1) % cards.length) {
                    card.classList.add('next');
                } else {
                    // This logic ensures cards far away are hidden properly
                    const d = index - currentIndex;
                    if (Math.abs(d) > 1) {
                         if (d > 0 && (d < cards.length/2) || d < -cards.length/2) {
                            card.classList.add('hidden-right');
                        } else {
                            card.classList.add('hidden-left');
                        }
                    }
                }
            });
        }

        function showNext() {
            currentIndex = (currentIndex + 1) % cards.length;
            setCardPositions();
        }

        function showPrev() {
            currentIndex = (currentIndex - 1 + cards.length) % cards.length;
            setCardPositions();
        }

        document.getElementById('next-btn').addEventListener('click', showNext);
        document.getElementById('prev-btn').addEventListener('click', showPrev);

        // Swipe functionality for mouse and touch
        cards.forEach(card => {
            card.addEventListener('mousedown', dragStart);
            card.addEventListener('touchstart', dragStart, { passive: true });

            card.addEventListener('mouseup', dragEnd);
            card.addEventListener('touchend', dragEnd);
            card.addEventListener('mouseleave', dragEnd);

            card.addEventListener('mousemove', drag);
            card.addEventListener('touchmove', drag, { passive: true });
        });
        
        function dragStart(event) {
            isDragging = true;
            startPos = getPositionX(event);
            
            // Apply a class to the card for cursor style
            const activeCard = cards[currentIndex];
            if(activeCard) activeCard.classList.add('grabbing');
        }
        
        function drag(event) {
            if (isDragging) {
                const currentPosition = getPositionX(event);
                currentTranslate = currentPosition - startPos;
            }
        }

        function dragEnd() {
            if (!isDragging) return;
            isDragging = false;
            
            if (currentTranslate < -100) { // Swipe left
                showNext();
            } else if (currentTranslate > 100) { // Swipe right
                showPrev();
            }
            
            currentTranslate = 0; // Reset
            const activeCard = cards[currentIndex];
            if(activeCard) activeCard.classList.remove('grabbing');
        }
        
        function getPositionX(event) {
            return event.type.includes('mouse') ? event.clientX : event.touches[0].clientX;
        }

        // Disable context menu on drag to prevent interference
        window.oncontextmenu = function(event) {
            if (isDragging) {
                event.preventDefault();
                event.stopPropagation();
                return false;
            }
        };

        // Initialize slider
        setCardPositions();
    }
        // --- Scroll Animation for Luxury Awards --- //
    const awardItems = document.querySelectorAll('.luxury-award-item');

    if (awardItems.length > 0) {
        const awardObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('is-visible');
                    observer.unobserve(entry.target); // Stop observing once it's visible
                }
            });
        }, {
            threshold: 0.1 // Trigger when 10% of the item is visible
        });

        awardItems.forEach(item => {
            awardObserver.observe(item);
        });
    }
    
});

