<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blog & Stories | Atfel Kasbah Hotel</title>
    <meta name="description" content="Discover stories, travel guides, and news from Atfel Kasbah and the beautiful Ourika Valley.">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="hamburger-menu-fix.css">
    <link rel="stylesheet" href="assets/css/language-switcher.css">
    
    <!-- Fallback for when JavaScript is disabled -->
    <noscript>
        <style>
            .mobile-nav-toggle { display: none; }
            .nav-links { 
                display: block !important; 
                position: static !important;
                transform: none !important;
                background: transparent !important;
                width: 100% !important;
                pointer-events: auto !important;
            }
        </style>
    </noscript>
    <style>
        :root {
            --primary-color: #8B4513;
            --secondary-color: #f7f5f2;
            --text-dark: #2a2a2a;
            --text-light: #6b6b6b;
            --white: #ffffff;
            --border-color: #e5e0da;
        }

        .blog-hero {
            padding-top: 120px;
            padding-bottom: 60px;
            background-color: var(--white);
            text-align: center;
        }

        .blog-hero h1 {
            font-size: 3.5rem;
            color: var(--text-dark);
            font-weight: 700;
        }

        .blog-hero p {
            font-size: 1.25rem;
            color: var(--text-light);
            max-width: 600px;
            margin: 1rem auto 0;
        }

        .blog-grid-section {
            padding-top: 40px;
            padding-bottom: 60px;
            background-color: var(--secondary-color);
        }

        .blog-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 2rem;
        }

        .blog-card {
            background-color: var(--white);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            display: flex;
            flex-direction: column;
        }

        .blog-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.1);
        }

        .card-image {
            height: 220px;
        }

        .card-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .card-content {
            padding: 1.5rem;
            display: flex;
            flex-direction: column;
            flex-grow: 1; /* Allows footer to stick to bottom */
        }
        
        .card-category {
            display: inline-block;
            background-color: var(--secondary-color);
            color: var(--primary-color);
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-bottom: 1rem;
            text-transform: uppercase;
        }

        .card-content h2 {
            font-size: 1.5rem;
            color: var(--text-dark);
            margin-bottom: 1rem;
            line-height: 1.3;
        }
        
        .card-excerpt {
            color: var(--text-light);
            line-height: 1.6;
            margin-bottom: 1.5rem;
            flex-grow: 1;
        }

        .card-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: var(--text-light);
            font-size: 0.9rem;
        }
        
        .btn-read-more {
            color: var(--primary-color);
            font-weight: 600;
            text-decoration: none;
        }
        .btn-read-more:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <header id="header">
        <nav class="container">
            <a href="index.html" class="logo">Atfel Kasbah</a>
            <button class="mobile-nav-toggle" aria-controls="primary-navigation" aria-expanded="false"><span class="line line-top"></span><span class="line line-middle"></span><span class="line line-bottom"></span></button>
            <ul id="primary-navigation" data-visible="false" class="nav-links">
                <li><a href="index.html#home">Home</a></li>
                <li><a href="index.html#rooms">Rooms</a></li>
                <li><a href="gallery.html">Gallery</a></li>
                <li class="cta-item"><a href="index.html#rooms" class="cta-button">Book now</a></li>
                <li><a href="aboutus.html">About Us</a></li>
                <li><a href="contactus.html">Contact</a></li>

                <!-- Language Switcher -->
                <li class="language-switcher">
                    <div class="language-dropdown">
                        <button class="language-btn" aria-label="Switch language">
                            <span class="flag-icon flag-en"></span>
                            <span class="language-text">EN</span>
                        </button>
                        <div class="language-menu">
                            <a href="blog.html" class="language-option active" data-lang="en">
                                <span class="flag-icon flag-en"></span>
                                <span>English</span>
                            </a>
                            <a href="blog-fr.html" class="language-option" data-lang="fr">
                                <span class="flag-icon flag-fr"></span>
                                <span>Français</span>
                            </a>
                        </div>
                    </div>
                </li>
            </ul>
        </nav>
    </header>

    <main>
        <section class="blog-hero">
            <div class="container">
                <h1>From Our Kasbah</h1>
                <p>Journal, stories, and travel guides to help you experience the best of the Ourika Valley.</p>
            </div>
        </section>
        <section class="blog-grid-section">
            <div class="container blog-grid">
                <!-- Blog Post 1 -->
                <article class="blog-card">
                    <div class="card-image"><img src="assets/image.png" alt="Peaceful courtyard of Atfel Kasbah"></div>
                    <div class="card-content">
                        <span class="card-category">Hotel Life</span>
                        <h2>Discover Atfel Kasbah: An Oasis of Tranquility</h2>
                        <p class="card-excerpt">Step inside our doors and explore what makes Atfel Kasbah a unique sanctuary of peace, from its authentic architecture to its serene atmosphere.</p>
                        <div class="card-footer">
                            <span>July 15, 2025</span>
                            <a href="blog-post-1.html" class="btn-read-more">Read More →</a>
                        </div>
                    </div>
                </article>

                <!-- Blog Post 2 -->
                <article class="blog-card">
                    <div class="card-image"><img src="Gallery-Hotel_Kasbah_Atfel_Ourika/Famous-place in-morocco-Ourika-Valley-Mountain-View.jpg" alt="Hiking in the Ourika Valley"></div>
                    <div class="card-content">
                        <span class="card-category">Adventure</span>
                        <h2>Top 5 Unforgettable Activities in the Ourika Valley</h2>
                        <p class="card-excerpt">Your adventure starts here. We've curated the top five must-do activities in the Ourika Valley, from waterfall hikes to bustling Berber markets.</p>
                        <div class="card-footer">
                            <span>July 10, 2025</span>
                            <a href="blog-post-2.html" class="btn-read-more">Read More →</a>
                        </div>
                    </div>
                </article>

                <!-- Blog Post 3 -->
                <article class="blog-card">
                    <div class="card-image"><img src="assets/image copy 6.png" alt="A delicious Moroccan tagine"></div>
                    <div class="card-content">
                        <span class="card-category">Cuisine</span>
                        <h2>A Culinary Journey: The Flavors of Atfel Kasbah</h2>
                        <p class="card-excerpt">Taste the soul of Morocco. A look into our farm-to-table philosophy and the traditional flavors that make our dining experience so memorable.</p>
                         <div class="card-footer">
                            <span>July 5, 2025</span>
                            <a href="blog-post-3.html" class="btn-read-more">Read More →</a>
                        </div>
                    </div>
                </article>
            </div>
        </section>
    </main>
    <!-- =========================================
       START: Custom Testimonial Slider
       ========================================= -->
    <section class="testimonial-slider-section">
        <div class="container">
            <h2 class="section-title">What Our Guests Say</h2>
        </div>

        <div class="testimonial-slider-container" id="testimonial-slider">
            <!-- Testimonial Cards will be dynamically inserted here by JavaScript -->
        </div>

        <div class="slider-nav">
            <button id="prev-btn" class="slider-btn" aria-label="Previous testimonial">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
                </svg>
            </button>
            <button id="next-btn" class="slider-btn" aria-label="Next testimonial">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                </svg>
            </button>
        </div>
    </section>
    <!-- =========================================
       END: Custom Testimonial Slider
       ========================================= -->
    
    <footer id="footer">
        <!-- Paste your updated footer code here -->
        <div class="container">
            <div class="footer-content">
                <div class="footer-section"><h3>Atfel Kasbah</h3><p>Experience authentic Moroccan hospitality in the heart of Ourika Valley.</p></div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="index.html#rooms">Rooms & Suites</a></li>
                        <li><a href="gallery.html">Gallery</a></li>
                        <li><a href="aboutus.html">About Us</a></li>
                        <li><a href="blog.html">Blog / News</a></li>
                        <li><a href="faq.html">FAQ</a></li>
                        <li><a href="contactus.html">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Contact Info</h3>
                    <ul><li>📍 Ourika Valley, Morocco</li><li>📞 +212 XXX XXXXXX</li><li>✉️ <EMAIL></li></ul>
                </div>
                <div class="footer-section">
                    <h3>Legal</h3>
                    <ul><li><a href="#">Privacy Policy</a></li><li><a href="#">Terms & Conditions</a></li></ul>
                </div>
            </div>
            <div class="footer-bottom"><p>© 2025 Atfel Kasbah Hotel. All rights reserved.</p></div>
        </div>
    </footer>
    
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const navToggle = document.querySelector('.mobile-nav-toggle');
            const primaryNav = document.getElementById('primary-navigation');
            navToggle.addEventListener('click', () => {
                const isVisible = primaryNav.getAttribute('data-visible') === 'true';
                primaryNav.setAttribute('data-visible', !isVisible);
                navToggle.setAttribute('aria-expanded', !isVisible);
            });
        });
    </script>
    <script src="assets/js/main.js" defer></script>
    <script src="hamburger-menu-fix.js" defer></script>
    <script src="assets/js/language-switcher.js" defer></script>
</body>
</html>
