// Language Switcher JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Get language switcher elements
    const languageBtns = document.querySelectorAll('.language-btn');
    const languageMenus = document.querySelectorAll('.language-menu');
    
    // Toggle dropdown when clicking the language button
    languageBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // Get the parent dropdown's menu
            const menu = this.nextElementSibling;
            
            // Close all other menus first
            languageMenus.forEach(otherMenu => {
                if (otherMenu !== menu) {
                    otherMenu.classList.remove('show');
                }
            });
            
            // Toggle this menu
            menu.classList.toggle('show');
        });
    });
    
    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
        // Check if the click is outside all language switchers
        const isClickInsideLanguageSwitcher = e.target.closest('.language-switcher');
        
        if (!isClickInsideLanguageSwitcher) {
            languageMenus.forEach(menu => {
                menu.classList.remove('show');
            });
        }
    });
    
    // Prevent clicks inside the menu from closing it
    languageMenus.forEach(menu => {
        menu.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    });
    
    // Initialize language based on current page
    const currentPath = window.location.pathname;
    const isFrenchPage = currentPath.includes('-fr.html') || currentPath.includes('/fr/');
    const currentLanguage = isFrenchPage ? 'fr' : 'en';
    
    // Update active language in UI
    document.querySelectorAll('.language-option').forEach(option => {
        const optionLang = option.getAttribute('data-lang');
        if (optionLang === currentLanguage) {
            option.classList.add('active');
        } else {
            option.classList.remove('active');
        }
    });
    
    // Update language button display
    document.querySelectorAll('.language-btn').forEach(btn => {
        const flagIcon = btn.querySelector('.flag-icon');
        const langText = btn.querySelector('.language-text');
        
        if (flagIcon) {
            flagIcon.className = `flag-icon flag-${currentLanguage}`;
        }
        
        if (langText) {
            langText.textContent = currentLanguage.toUpperCase();
        }
    });
});