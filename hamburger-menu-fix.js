document.addEventListener('DOMContentLoaded', () => {
    try {
        console.log('Hamburger menu fix script loaded');
        const primaryNav = document.getElementById('primary-navigation');
        const navToggle = document.querySelector('.mobile-nav-toggle');

        if (navToggle && primaryNav) {
            console.log('Mobile navigation elements found');

            // Add our event listener for the hamburger menu
            navToggle.addEventListener('click', (e) => {
                console.log('Hamburger menu clicked');
                const isVisible = primaryNav.getAttribute('data-visible') === 'true';
                console.log('Current visibility:', isVisible);

                // Toggle the menu visibility
                primaryNav.setAttribute('data-visible', !isVisible);
                navToggle.setAttribute('aria-expanded', !isVisible);
                console.log('Menu visibility toggled to:', !isVisible);

                // Prevent event bubbling
                e.stopPropagation();
            });

            // Close menu when clicking outside
            document.addEventListener('click', (e) => {
                if (primaryNav.getAttribute('data-visible') === 'true' &&
                    !primaryNav.contains(e.target) &&
                    e.target !== navToggle &&
                    !navToggle.contains(e.target)) {
                    primaryNav.setAttribute('data-visible', 'false');
                    navToggle.setAttribute('aria-expanded', 'false');
                    console.log('Menu closed by clicking outside');
                }
            });

            // Fix for all navigation links in mobile menu
            const navLinks = primaryNav.querySelectorAll('a');
            navLinks.forEach(link => {
                link.addEventListener('click', () => {
                    // Close the menu when any link is clicked
                    primaryNav.setAttribute('data-visible', 'false');
                    navToggle.setAttribute('aria-expanded', 'false');
                });
            });

            // Make sure the Book Now button links to the rooms section
            const bookNowButton = primaryNav.querySelector('#header-cta-button');
            if (bookNowButton) {
                bookNowButton.setAttribute('href', '#rooms');
            }
        } else {
            console.error('Mobile navigation elements not found');
        }
    } catch (error) {
        console.error('Error in hamburger menu fix:', error);
    }
});