document.addEventListener('DOMContentLoaded', () => {
    try {
        console.log('Hamburger menu fix script loaded');
        const primaryNav = document.getElementById('primary-navigation');
        const navToggle = document.querySelector('.mobile-nav-toggle');

        if (navToggle && primaryNav) {
            console.log('Mobile navigation elements found');

            // Add our event listener for the hamburger menu
            navToggle.addEventListener('click', (e) => {
                console.log('Hamburger menu clicked');
                const isVisible = primaryNav.getAttribute('data-visible') === 'true';
                console.log('Current visibility:', isVisible);

                // Toggle the menu visibility
                primaryNav.setAttribute('data-visible', !isVisible);
                navToggle.setAttribute('aria-expanded', !isVisible);
                console.log('Menu visibility toggled to:', !isVisible);

                // Add/remove body class to prevent scrolling when menu is open
                if (!isVisible) {
                    document.body.classList.add('nav-open');
                } else {
                    document.body.classList.remove('nav-open');
                }

                // Prevent event bubbling
                e.stopPropagation();
            });

            // Close menu when clicking outside
            document.addEventListener('click', (e) => {
                if (primaryNav.getAttribute('data-visible') === 'true' &&
                    !primaryNav.contains(e.target) &&
                    e.target !== navToggle &&
                    !navToggle.contains(e.target)) {
                    closeMenu();
                    console.log('Menu closed by clicking outside');
                }
            });

            // Close menu on escape key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && primaryNav.getAttribute('data-visible') === 'true') {
                    closeMenu();
                    console.log('Menu closed by escape key');
                }
            });

            // Function to close menu
            function closeMenu() {
                primaryNav.setAttribute('data-visible', 'false');
                navToggle.setAttribute('aria-expanded', 'false');
                document.body.classList.remove('nav-open');
            }

            // Fix for all navigation links in mobile menu (excluding language switcher)
            const navLinks = primaryNav.querySelectorAll('a:not(.language-option)');
            navLinks.forEach(link => {
                link.addEventListener('click', () => {
                    // Don't close menu if it's a language switcher button
                    if (!link.closest('.language-switcher')) {
                        closeMenu();
                    }
                });
            });

            // Handle language switcher dropdown
            const languageBtn = primaryNav.querySelector('.language-btn');
            const languageMenu = primaryNav.querySelector('.language-menu');

            if (languageBtn && languageMenu) {
                languageBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    languageMenu.classList.toggle('active');
                });

                // Close language dropdown when clicking outside
                document.addEventListener('click', (e) => {
                    if (!languageBtn.contains(e.target) && !languageMenu.contains(e.target)) {
                        languageMenu.classList.remove('active');
                    }
                });

                // Handle language option clicks
                const languageOptions = languageMenu.querySelectorAll('.language-option');
                languageOptions.forEach(option => {
                    option.addEventListener('click', () => {
                        languageMenu.classList.remove('active');
                        closeMenu();
                    });
                });
            }

            // Make sure the Book Now button links to the rooms section
            const bookNowButton = primaryNav.querySelector('#header-cta-button, .cta-button');
            if (bookNowButton && !bookNowButton.getAttribute('href').includes('http')) {
                // Only modify if it's not an external link
                const currentHref = bookNowButton.getAttribute('href');
                if (!currentHref.includes('#rooms')) {
                    if (window.location.pathname.includes('index')) {
                        bookNowButton.setAttribute('href', '#rooms');
                    } else {
                        bookNowButton.setAttribute('href', 'index.html#rooms');
                    }
                }
            }

            // Handle window resize
            window.addEventListener('resize', () => {
                if (window.innerWidth > 768) {
                    closeMenu();
                }
            });

        } else {
            console.error('Mobile navigation elements not found');
        }
    } catch (error) {
        console.error('Error in hamburger menu fix:', error);
    }
});