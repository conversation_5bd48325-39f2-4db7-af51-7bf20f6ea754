/* Hamburger Menu Fix CSS */
@media (max-width: 920px) {

    /* Hamburger button styling */
    .mobile-nav-toggle {
        display: block !important;
        position: absolute;
        z-index: 1002 !important;
        /* Increased z-index to be above the nav-links */
        top: 1.5rem;
        right: 2rem;
        width: 2.5rem;
        height: 2.5rem;
        background: transparent;
        border: 0;
        cursor: pointer;
    }

    /* Hamburger icon lines */
    .mobile-nav-toggle .line {
        display: block;
        width: 100%;
        height: 3px;
        background-color: var(--text-charcoal);
        border-radius: 2px;
        transition: transform 0.3s ease-out, opacity 0.2s ease-out;
    }

    .mobile-nav-toggle .line+.line {
        margin-top: 6px;
    }

    /* X transformation when menu is open */
    .mobile-nav-toggle[aria-expanded="true"] .line-top {
        transform: translateY(9px) rotate(45deg);
    }

    .mobile-nav-toggle[aria-expanded="true"] .line-middle {
        opacity: 0;
    }

    .mobile-nav-toggle[aria-expanded="true"] .line-bottom {
        transform: translateY(-9px) rotate(-45deg);
    }



    /* Navigation menu styling */
    #primary-navigation {
        position: fixed;
        top: 0;
        right: 0;
        width: 70vw;
        height: 100vh;
        z-index: 1001 !important;
        /* Ensure proper stacking */
        flex-direction: column;
        padding: min(20vh, 10rem) 2em;
        gap: 2em;
        justify-content: flex-start;
        align-items: center;
        background: hsla(38, 33%, 97%, 0.95);
        backdrop-filter: blur(1rem);
        transform: translateX(100%);
        transition: transform 350ms ease-out;
    }

    /* Menu visibility states */
    #primary-navigation[data-visible="false"] {
        transform: translateX(100%);
        pointer-events: none !important;
        /* Disable pointer events when hidden */
    }

    #primary-navigation[data-visible="true"] {
        transform: translateX(0%);
        pointer-events: auto !important;
        /* Enable pointer events when visible */
    }

    /* Ensure all navigation links are clickable */
    .nav-links li {
        pointer-events: auto !important;
        width: 100%;
        text-align: center;
    }

    .nav-links a {
        pointer-events: auto !important;
        display: block;
        width: 100%;
        padding: 1rem;
        touch-action: manipulation;
    }

    /* Change mobile text links to be larger */
    .nav-links a:not(.cta-button) {
        font-size: 1.5rem;
    }

    /* Fix for mobile CTA button */
    li.cta-item {
        display: block !important;
        margin: 1rem 0 !important;
        max-width: 100% !important;
        opacity: 1 !important;
        overflow: visible !important;
        pointer-events: auto !important;
    }

    /* Fix for room card action area on mobile */
    .room-action-area {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        width: 100%;
    }

    .room-action-area .room-price {
        flex: 1;
        text-align: left;
        margin-right: 16px;
        margin-bottom: 15px;
    }

    .room-action-area .btn-primary {
        min-width: 120px;
        padding: 10px 16px;
        font-size: 16px;
        white-space: nowrap;
        text-align: center;
    }
}